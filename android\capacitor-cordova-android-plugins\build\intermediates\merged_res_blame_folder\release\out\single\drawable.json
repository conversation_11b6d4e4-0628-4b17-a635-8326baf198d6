[{"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/notification_bg_low.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-core-1.9.0-4:/drawable/notification_bg_low.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_switch_thumb_material.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_switch_thumb_material.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/notification_icon_background.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-core-1.9.0-4:/drawable/notification_icon_background.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_list_selector_holo_dark.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_list_selector_holo_dark.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_list_selector_holo_light.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_list_selector_holo_light.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_ic_menu_copy_mtrl_am_alpha.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_ic_menu_copy_mtrl_am_alpha.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_tab_indicator_material.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_tab_indicator_material.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/btn_checkbox_unchecked_mtrl.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/btn_checkbox_unchecked_mtrl.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_ic_arrow_drop_right_black_24dp.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_ic_arrow_drop_right_black_24dp.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_star_half_black_48dp.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_star_half_black_48dp.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/btn_radio_off_mtrl.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/btn_radio_off_mtrl.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/btn_radio_on_mtrl.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/btn_radio_on_mtrl.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_item_background_holo_dark.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_item_background_holo_dark.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_list_selector_background_transition_holo_dark.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_list_selector_background_transition_holo_dark.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_ic_voice_search_api_material.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_ic_voice_search_api_material.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_text_cursor_material.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_text_cursor_material.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/tooltip_frame_light.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/tooltip_frame_light.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_ic_go_search_api_material.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_ic_go_search_api_material.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_cab_background_top_material.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_cab_background_top_material.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_star_black_48dp.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_star_black_48dp.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_cab_background_internal_bg.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_cab_background_internal_bg.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_ic_ab_back_material.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_ic_ab_back_material.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_btn_default_mtrl_shape.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_btn_default_mtrl_shape.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_spinner_textfield_background_material.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_spinner_textfield_background_material.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_ic_menu_paste_mtrl_am_alpha.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_ic_menu_paste_mtrl_am_alpha.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_ic_menu_selectall_mtrl_alpha.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_ic_menu_selectall_mtrl_alpha.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/btn_checkbox_checked_mtrl.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/btn_checkbox_checked_mtrl.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/btn_checkbox_unchecked_to_checked_mtrl_animation.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/btn_checkbox_unchecked_to_checked_mtrl_animation.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_ratingbar_material.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_ratingbar_material.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/btn_radio_off_to_on_mtrl_animation.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/btn_radio_off_to_on_mtrl_animation.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_btn_check_material_anim.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_btn_check_material_anim.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_btn_radio_material.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_btn_radio_material.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_btn_check_material.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_btn_check_material.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_seekbar_tick_mark_material.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_seekbar_tick_mark_material.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/btn_checkbox_checked_to_unchecked_mtrl_animation.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/btn_checkbox_checked_to_unchecked_mtrl_animation.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/tooltip_frame_dark.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/tooltip_frame_dark.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/test_level_drawable.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/test_level_drawable.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/btn_radio_on_to_off_mtrl_animation.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/btn_radio_on_to_off_mtrl_animation.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_ic_menu_overflow_material.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_ic_menu_overflow_material.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_vector_test.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-resources-1.6.1-10:/drawable/abc_vector_test.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_ic_menu_share_mtrl_alpha.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_ic_menu_share_mtrl_alpha.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_textfield_search_material.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_textfield_search_material.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/notification_bg.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-core-1.9.0-4:/drawable/notification_bg.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/notification_tile_bg.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-core-1.9.0-4:/drawable/notification_tile_bg.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_seekbar_thumb_material.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_seekbar_thumb_material.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_item_background_holo_light.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_item_background_holo_light.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_btn_radio_material_anim.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_btn_radio_material_anim.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_btn_borderless_material.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_btn_borderless_material.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_seekbar_track_material.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_seekbar_track_material.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_ratingbar_small_material.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_ratingbar_small_material.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_list_selector_background_transition_holo_light.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_list_selector_background_transition_holo_light.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_ic_search_api_material.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_ic_search_api_material.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_ic_clear_material.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_ic_clear_material.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_ic_menu_cut_mtrl_alpha.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_ic_menu_cut_mtrl_alpha.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/drawable/abc_ratingbar_indicator_material.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/drawable/abc_ratingbar_indicator_material.xml"}]