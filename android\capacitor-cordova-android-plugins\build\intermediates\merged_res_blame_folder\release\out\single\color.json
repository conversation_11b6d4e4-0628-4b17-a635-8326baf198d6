[{"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/color/abc_primary_text_material_light.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/color/abc_primary_text_material_light.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/color/abc_hint_foreground_material_dark.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/color/abc_hint_foreground_material_dark.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/color/abc_btn_colored_text_material.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/color/abc_btn_colored_text_material.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/color/abc_hint_foreground_material_light.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/color/abc_hint_foreground_material_light.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/color/switch_thumb_material_light.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/color/switch_thumb_material_light.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/color/abc_tint_seek_thumb.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/color/abc_tint_seek_thumb.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/color/abc_tint_spinner.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/color/abc_tint_spinner.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/color/abc_search_url_text.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/color/abc_search_url_text.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/color/abc_tint_btn_checkable.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/color/abc_tint_btn_checkable.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/color/abc_background_cache_hint_selector_material_light.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/color/abc_background_cache_hint_selector_material_light.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/color/abc_primary_text_material_dark.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/color/abc_primary_text_material_dark.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/color/abc_primary_text_disable_only_material_light.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/color/abc_primary_text_disable_only_material_light.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/color/abc_tint_default.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/color/abc_tint_default.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/color/abc_background_cache_hint_selector_material_dark.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/color/abc_background_cache_hint_selector_material_dark.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/color/abc_secondary_text_material_dark.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/color/abc_secondary_text_material_dark.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/color/abc_primary_text_disable_only_material_dark.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/color/abc_primary_text_disable_only_material_dark.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/color/abc_secondary_text_material_light.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/color/abc_secondary_text_material_light.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/color/switch_thumb_material_dark.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/color/switch_thumb_material_dark.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/color/abc_tint_switch_track.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/color/abc_tint_switch_track.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/color/abc_tint_edittext.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-appcompat-1.6.1-7:/color/abc_tint_edittext.xml"}]