[{"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/layout-v21/notification_template_icon_group.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-core-1.9.0-4:/layout-v21/notification_template_icon_group.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/layout-v21/notification_action.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-core-1.9.0-4:/layout-v21/notification_action.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/layout-v21/notification_action_tombstone.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-core-1.9.0-4:/layout-v21/notification_action_tombstone.xml"}, {"merged": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/layout-v21/notification_template_custom_big.xml", "source": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-core-1.9.0-4:/layout-v21/notification_template_custom_big.xml"}]