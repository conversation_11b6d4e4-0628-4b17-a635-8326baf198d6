{"logs": [{"outputFile": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-merged_res-19:/values-watch-v20/values-watch-v20.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4ce7c60d257b8629ab4c81cf615a92a1\\transformed\\appcompat-1.6.1\\res\\values-watch-v20\\values-watch-v20.xml", "from": {"startLines": "2,5,8", "startColumns": "4,4,4", "startOffsets": "55,214,385", "endLines": "4,7,10", "endColumns": "12,12,12", "endOffsets": "209,380,553"}}]}, {"outputFile": "capacitor.cordova.android.plugins.capacitor-cordova-android-plugins-mergeReleaseResources-17:/values-watch-v20/values-watch-v20.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4ce7c60d257b8629ab4c81cf615a92a1\\transformed\\appcompat-1.6.1\\res\\values-watch-v20\\values-watch-v20.xml", "from": {"startLines": "2,5,8", "startColumns": "4,4,4", "startOffsets": "55,214,385", "endLines": "4,7,10", "endColumns": "12,12,12", "endOffsets": "209,380,553"}}]}]}